import sys
import os
import json
import yaml  # 添加yaml库导入
import subprocess
import threading
import base64
import requests
import urllib.parse
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QTableWidget, QTableWidgetItem, QHeaderView, QCheckBox, QLabel, QLineEdit,
    QSpinBox, QDoubleSpinBox, QComboBox, QPushButton, QGroupBox, QTextEdit,
    QMessageBox, QFileDialog, QDialog, QInputDialog, QMenu, QListWidget, QListWidgetItem
)
from PyQt5.QtCore import Qt, pyqtSignal, QObject
from PyQt5.QtGui import QIcon
import copy

# pyinstaller --onefile --windowed --add-data "subconverter;subconverter" --add-data "verge-mihomo.exe;." --add-data "config;config" app.py
# 定义测试URL常量
TEST_URLS = {
    "Google": "https://www.google.com/",
    "ChatGPT Web": "https://api.openai.com/compliance/cookie_requirements",
    "ChatGPT Auth": "https://chat.openai.com/api/auth/session",
    "ChatGPT API": "https://api.openai.com/v1/chat/completions",
    "OpenAI": "https://openai.com/"
}

class SignalManager(QObject):
    update_feedback = pyqtSignal(str)
    update_table = pyqtSignal(list)
    test_complete = pyqtSignal()
    parsing_complete = pyqtSignal(object)  # 修改为接收任意对象，用于传递节点列表和原始内容

class SubscriptionListDialog(QDialog):
    def __init__(self, parent=None, subscriptions=None):
        super().__init__(parent)
        self.setWindowTitle("订阅列表")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)
        
        # 初始化订阅列表
        self.subscriptions = subscriptions if subscriptions else []
        self.parent = parent
        
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["名称", "URL", "状态"])
        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.table.itemDoubleClicked.connect(self.on_item_double_clicked)
        layout.addWidget(self.table)
        
        # 设置右键菜单
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)
        
        # 创建按钮区域
        button_layout = QHBoxLayout()
        self.add_button = QPushButton("添加")
        self.save_close_button = QPushButton("保存并关闭")
        
        button_layout.addWidget(self.add_button)
        button_layout.addStretch(1)
        button_layout.addWidget(self.save_close_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.add_button.clicked.connect(self.add_subscription)
        self.save_close_button.clicked.connect(self.accept)
        self.table.doubleClicked.connect(self.edit_subscription)
        
        # 更新表格
        self.update_table()
        
    def on_item_double_clicked(self, item):
        row = item.row()
        url = self.subscriptions[row].get("url", "")
        if url and self.parent and self.subscriptions[row].get("enabled", True):
            self.parent.load_subscription(url)
            self.accept()
        
    def update_table(self):
        self.table.setRowCount(len(self.subscriptions))
        for i, sub in enumerate(self.subscriptions):
            self.table.setItem(i, 0, QTableWidgetItem(sub.get("name", "")))
            self.table.setItem(i, 1, QTableWidgetItem(sub.get("url", "")))
            status = "启用" if sub.get("enabled", True) else "禁用"
            self.table.setItem(i, 2, QTableWidgetItem(status))
    
    def add_subscription(self):
        dialog = AddSubscriptionDialog(self)
        result = dialog.exec_()
        if result == QDialog.Accepted:
            subscription = dialog.get_subscription()
            if subscription["url"]:  # 只要有URL就可以添加，名称可以使用默认值
                if not subscription["name"]:  # 如果名称为空，使用默认值
                    subscription["name"] = "订阅"
                self.subscriptions.append(subscription)
                self.update_table()
        elif result == 2:  # 删除操作，但在添加时不应该发生
            pass
    
    def edit_subscription(self):
        selected_rows = self.table.selectedIndexes()
        if not selected_rows:
            return
            
        row = selected_rows[0].row()
        dialog = AddSubscriptionDialog(self, True, self.subscriptions[row])
        result = dialog.exec_()
        
        if result == QDialog.Accepted:
            self.subscriptions[row] = dialog.get_subscription()
            self.update_table()
        elif result == 2:  # 删除操作
            self.delete_subscription(row)
    
    def delete_subscription(self, row):
        if 0 <= row < len(self.subscriptions):
            del self.subscriptions[row]
            self.update_table()
    
    def get_subscriptions(self):
        return self.subscriptions
    
    def get_selected_url(self):
        selected_rows = self.table.selectedIndexes()
        if selected_rows:
            row = selected_rows[0].row()
            if self.subscriptions[row].get("enabled", True):
                return self.subscriptions[row].get("url", "")
        return ""

    def show_context_menu(self, position):
        # 获取选中行
        selected_indexes = self.table.selectedIndexes()
        if not selected_indexes:
            return
            
        row = selected_indexes[0].row()
        
        # 创建菜单
        menu = QMenu(self)
        edit_action = menu.addAction("编辑订阅")
        
        # 根据当前状态添加启用/禁用选项
        if self.subscriptions[row].get("enabled", True):
            enable_action = menu.addAction("禁用订阅")
        else:
            enable_action = menu.addAction("启用订阅")
            
        menu.addSeparator()
        copy_url_action = menu.addAction("复制订阅URL")
        
        menu.addSeparator()
        delete_action = menu.addAction("删除订阅")
        
        # 显示菜单
        action = menu.exec_(self.table.mapToGlobal(position))
        
        # 处理菜单选择
        if action == edit_action:
            self.edit_subscription()
        elif action == enable_action:
            self.toggle_subscription_status(row)
        elif action == copy_url_action:
            self.copy_subscription_url(row)
        elif action == delete_action:
            self.delete_subscription(row)
            
    def toggle_subscription_status(self, row):
        """切换订阅的启用/禁用状态"""
        if 0 <= row < len(self.subscriptions):
            current_status = self.subscriptions[row].get("enabled", True)
            self.subscriptions[row]["enabled"] = not current_status
            self.update_table()
            
    def copy_subscription_url(self, row):
        """复制订阅URL到剪贴板"""
        if 0 <= row < len(self.subscriptions):
            url = self.subscriptions[row].get("url", "")
            if url:
                clipboard = QApplication.clipboard()
                clipboard.setText(url)
                
    def keyPressEvent(self, event):
        """处理键盘事件，实现快捷键功能"""
        # Insert: 添加订阅
        if event.key() == Qt.Key_Insert:
            self.add_subscription()
            
        # Delete: 删除选中的订阅
        elif event.key() == Qt.Key_Delete:
            selected_rows = self.table.selectedIndexes()
            if selected_rows:
                row = selected_rows[0].row()
                self.delete_subscription(row)
                
        # Enter/Return: 编辑选中的订阅
        elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            selected_rows = self.table.selectedIndexes()
            if selected_rows:
                self.edit_subscription()
                
        # 其他键盘事件传递给父类处理
        else:
            super().keyPressEvent(event)

class AddSubscriptionDialog(QDialog):
    def __init__(self, parent=None, edit_mode=False, subscription=None):
        super().__init__(parent)
        self.setWindowTitle("订阅列表")
        self.setMinimumWidth(400)
        self.setMinimumHeight(300)
        
        # 初始化数据
        self.edit_mode = edit_mode
        self.subscription = subscription or {"name": "订阅", "url": "", "enabled": True}
        
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 添加名称输入行
        name_layout = QHBoxLayout()
        name_label = QLabel("备注:")
        self.name_edit = QLineEdit(self.subscription.get("name", "订阅"))
        self.enabled_checkbox = QCheckBox("启用")
        self.enabled_checkbox.setChecked(self.subscription.get("enabled", True))
        self.delete_button = QPushButton("删除")
        self.delete_button.setEnabled(True)  # 启用删除按钮
        
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_edit)
        name_layout.addWidget(self.enabled_checkbox)
        name_layout.addWidget(self.delete_button)
        layout.addLayout(name_layout)
        
        # 添加URL输入行
        url_layout = QHBoxLayout()
        url_label = QLabel("网址:")
        self.url_edit = QLineEdit(self.subscription.get("url", ""))
        url_layout.addWidget(url_label)
        url_layout.addWidget(self.url_edit)
        layout.addLayout(url_layout)
        
        # 添加空白区域
        layout.addStretch(1)
        
        # 添加底部按钮
        button_layout = QHBoxLayout()
        self.add_button = QPushButton("添加")
        self.save_close_button = QPushButton("保存并关闭")
        
        button_layout.addWidget(self.add_button)
        button_layout.addStretch(1)
        button_layout.addWidget(self.save_close_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.add_button.clicked.connect(self.accept)
        self.save_close_button.clicked.connect(self.accept)
        self.delete_button.clicked.connect(self.delete_subscription)
        
        # 设置URL输入框为焦点
        self.url_edit.setFocus()
        
    def get_subscription(self):
        return {
            "name": self.name_edit.text(),
            "url": self.url_edit.text(),
            "enabled": self.enabled_checkbox.isChecked()
        }
        
    def delete_subscription(self):
        # 在编辑模式下删除订阅，在添加模式下清空输入
        if self.edit_mode:
            self.done(2)  # 使用自定义返回值表示删除操作
        else:
            # 在添加模式下，清空输入
            self.name_edit.setText("订阅")
            self.url_edit.clear()
            self.enabled_checkbox.setChecked(True)
    
    def keyPressEvent(self, event):
        """处理键盘事件，实现回车键确认添加订阅的功能"""
        # 回车键确认添加
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            # 如果URL不为空，则接受对话框
            if self.url_edit.text().strip():
                self.accept()
            else:
                # URL为空，提示用户
                QMessageBox.warning(self, "提示", "请输入订阅URL")
        else:
            # 其他键盘事件传递给父类处理
            super().keyPressEvent(event)

class MainWindow(QMainWindow):
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 设置窗口基本属性
        self.setWindowTitle("节点自动测速工具(nodesCatch) - V2.0")
        self.setGeometry(100, 100, 1080, 600)
        self.setAcceptDrops(True)
        
        # 初始化数据和状态
        self.signal_manager = SignalManager()
        self.nodes = []
        self.testing = False
        self.config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.json")
        self.feedback_text = None  # 将在create_feedback_area中创建
        self.subconverter_process = None  # 存储subconverter进程
        self.mihomo_process = None  # 存储mihomo进程
        
        # 创建UI布局
        self._setup_ui()
        
        # 连接信号和槽
        self.connect_signals()
        
        # 加载配置并显示初始数据
        self.load_config()
        self.update_table_data(self.nodes)
        
        # 启动subconverter程序
        self.start_subconverter()

    def _setup_ui(self):
        """设置UI布局和组件"""
        # 创建中央部件和主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # 创建表格
        self.create_table()
        main_layout.addWidget(self.table_widget)

        # 创建控制区域
        self.create_controls_area()
        main_layout.addWidget(self.controls_group)

        # 创建底部操作区域
        self.create_bottom_actions()
        main_layout.addLayout(self.bottom_actions_layout)
        
        # 创建反馈区域
        self.create_feedback_area()
        main_layout.addWidget(self.feedback_group)

    def create_table(self):
        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(10)
        self.table_widget.setHorizontalHeaderLabels([
            "No", "类型", "别名", "服务器地址", "端口", 
            "加密方式", "传输协议", "订阅", "测试结果", "峰值速度"
        ])
        self.table_widget.horizontalHeader().setStretchLastSection(True)
        self.table_widget.setColumnWidth(0, 40)
        self.table_widget.setColumnWidth(1, 80)
        self.table_widget.setColumnWidth(2, 150)
        self.table_widget.setColumnWidth(3, 150)
        self.table_widget.setColumnWidth(4, 60)
        self.table_widget.setColumnWidth(5, 100)
        self.table_widget.setColumnWidth(6, 100)
        self.table_widget.setColumnWidth(7, 100)
        self.table_widget.setColumnWidth(8, 100)
        self.table_widget.setColumnWidth(9, 100)
        
        # 设置右键菜单
        self.table_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table_widget.customContextMenuRequested.connect(self.show_context_menu)
        
        # 添加表头点击排序功能
        self.table_widget.horizontalHeader().setSectionsClickable(True)
        self.table_widget.horizontalHeader().sectionClicked.connect(self.on_header_clicked)
        
        # 用于记录当前排序状态
        self.sort_column = -1  # 当前排序的列
        self.sort_order = Qt.AscendingOrder  # 当前排序顺序

    def create_controls_area(self):
        self.controls_group = QWidget()
        grid_layout = QGridLayout(self.controls_group)
        grid_layout.setSpacing(10)

        # Row 1
        self.fast_mode_checkbox = QCheckBox("启用快速模式")
        self.fast_mode_checkbox.setChecked(True)
        grid_layout.addWidget(self.fast_mode_checkbox, 0, 0)
        
        grid_layout.addWidget(QLabel("限定时间(秒):"), 0, 1, Qt.AlignRight)
        self.limit_time_spinbox = QSpinBox()
        self.limit_time_spinbox.setValue(5)
        self.limit_time_spinbox.setFixedWidth(60)
        grid_layout.addWidget(self.limit_time_spinbox, 0, 2)
        
        grid_layout.addWidget(QLabel("峰值速度(KB/s):"), 0, 3, Qt.AlignRight)
        self.peak_speed_spinbox = QSpinBox()
        self.peak_speed_spinbox.setMaximum(10000)
        self.peak_speed_spinbox.setValue(300)
        self.peak_speed_spinbox.setFixedWidth(60)
        grid_layout.addWidget(self.peak_speed_spinbox, 0, 4)

        grid_layout.addWidget(QLabel("下载进度(%):"), 0, 5, Qt.AlignRight)
        self.download_progress_spinbox = QSpinBox()
        self.download_progress_spinbox.setValue(10)
        self.download_progress_spinbox.setFixedWidth(60)
        grid_layout.addWidget(self.download_progress_spinbox, 0, 6)

        grid_layout.addWidget(QLabel("测延迟内核:"), 0, 7, Qt.AlignRight)
        self.latency_core_combo = QComboBox()
        self.latency_core_combo.addItems(["Clash", "V2Ray", "Xray"])
        grid_layout.addWidget(self.latency_core_combo, 0, 8)

        grid_layout.addWidget(QLabel("线程数:"), 0, 9, Qt.AlignRight)
        self.latency_threads_spinbox = QSpinBox()
        self.latency_threads_spinbox.setMaximum(1000)  # 先设置最大值
        self.latency_threads_spinbox.setValue(100)  # 再设置当前值
        self.latency_threads_spinbox.setFixedWidth(60)
        grid_layout.addWidget(self.latency_threads_spinbox, 0, 10)

        grid_layout.addWidget(QLabel("延迟超时(秒):"), 0, 11, Qt.AlignRight)
        self.latency_timeout_spinbox = QSpinBox()
        self.latency_timeout_spinbox.setValue(5)
        self.latency_timeout_spinbox.setFixedWidth(60)
        grid_layout.addWidget(self.latency_timeout_spinbox, 0, 12)

        # Row 2
        self.stop_manual_button = QPushButton("停止手动测速线程")
        self.stop_manual_button.setEnabled(False)
        grid_layout.addWidget(self.stop_manual_button, 1, 0)

        grid_layout.addWidget(QLabel("Clash外部控制端口:"), 1, 1, Qt.AlignRight)
        self.clash_port_edit = QLineEdit("9090")
        self.clash_port_edit.setFixedWidth(60)
        grid_layout.addWidget(self.clash_port_edit, 1, 2)
        
        self.subscription_button = QPushButton("订阅管理")
        grid_layout.addWidget(self.subscription_button, 1, 3)
        
        # 添加测试URL选择下拉框
        grid_layout.addWidget(QLabel("测试URL:"), 1, 4, Qt.AlignRight)
        self.test_url_combo = QComboBox()
        self.test_url_combo.addItems(TEST_URLS.keys())
        grid_layout.addWidget(self.test_url_combo, 1, 5)
        
        grid_layout.addWidget(QLabel("测下载内核:"), 1, 7, Qt.AlignRight)
        self.download_core_combo = QComboBox()
        self.download_core_combo.addItems(["Clash"])
        grid_layout.addWidget(self.download_core_combo, 1, 8)

        grid_layout.addWidget(QLabel("线程数:"), 1, 9, Qt.AlignRight)
        self.download_threads_spinbox = QSpinBox()
        self.download_threads_spinbox.setValue(5)
        self.download_threads_spinbox.setFixedWidth(60)
        grid_layout.addWidget(self.download_threads_spinbox, 1, 10)

        grid_layout.addWidget(QLabel("清空低速节点(MB/s):"), 1, 11, Qt.AlignRight)
        self.clear_low_speed_spinbox = QDoubleSpinBox()
        self.clear_low_speed_spinbox.setDecimals(1)  # 设置只显示一位小数
        self.clear_low_speed_spinbox.setSingleStep(0.1)  # 设置每次点击增减的值为0.1
        self.clear_low_speed_spinbox.setValue(0.6)
        self.clear_low_speed_spinbox.setFixedWidth(60)
        grid_layout.addWidget(self.clear_low_speed_spinbox, 1, 12)
        
        grid_layout.setColumnStretch(13, 1)

    def create_bottom_actions(self):
        self.bottom_actions_layout = QHBoxLayout()
        self.bottom_actions_layout.addStretch(1)

        self.bottom_actions_layout.addWidget(QLabel("延迟结果应小于(个):"))
        self.latency_result_limit_edit = QLineEdit("100")
        self.latency_result_limit_edit.setFixedWidth(50)
        self.bottom_actions_layout.addWidget(self.latency_result_limit_edit)

        self.test_latency_checkbox = QCheckBox("测延迟速度")
        self.test_latency_checkbox.setChecked(True)
        self.bottom_actions_layout.addWidget(self.test_latency_checkbox)
        
        self.test_download_checkbox = QCheckBox("测下载速度")
        self.test_download_checkbox.setChecked(True)
        self.bottom_actions_layout.addWidget(self.test_download_checkbox)
        
        self.auto_test_button = QPushButton("一键自动测速")
        self.bottom_actions_layout.addWidget(self.auto_test_button)
        
        self.save_config_button = QPushButton("保存配置")
        self.bottom_actions_layout.addWidget(self.save_config_button)
        
        self.about_button = QPushButton("关于软件")
        self.bottom_actions_layout.addWidget(self.about_button)

    def create_feedback_area(self):
        self.feedback_group = QGroupBox("反馈")
        layout = QVBoxLayout(self.feedback_group)
        self.feedback_text = QTextEdit()
        self.feedback_text.setReadOnly(True)
        layout.addWidget(self.feedback_text)
        self.feedback_group.setFixedHeight(100)
        
    def connect_signals(self):
        # 连接信号管理器信号
        self.signal_manager.update_feedback.connect(self.update_feedback_text)
        self.signal_manager.update_table.connect(self.update_table_data)
        self.signal_manager.test_complete.connect(self.on_test_complete)
        self.signal_manager.parsing_complete.connect(self.on_parsing_complete)  # 连接解析完成信号
        
        # 连接按钮信号
        self.subscription_button.clicked.connect(self.manage_subscription)
        self.auto_test_button.clicked.connect(self.start_auto_test)
        self.stop_manual_button.clicked.connect(self.stop_manual_test)
        self.save_config_button.clicked.connect(self.save_config)
        self.about_button.clicked.connect(self.show_about)
        
    def save_config(self):
        """保存当前配置到config.json文件"""
        try:
            # 获取当前配置
            config = {}
            
            # 如果配置文件已存在，先读取现有配置以保留其他设置
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            # 获取当前UI中的所有设置
            settings = {
                "fast_mode": self.fast_mode_checkbox.isChecked(),
                "limit_time": self.limit_time_spinbox.value(),
                "peak_speed": self.peak_speed_spinbox.value(),
                "download_progress": self.download_progress_spinbox.value(),
                "latency_core": self.latency_core_combo.currentText(),
                "latency_threads": self.latency_threads_spinbox.value(),
                "latency_timeout": self.latency_timeout_spinbox.value(),
                "clash_port": self.clash_port_edit.text(),
                "download_core": self.download_core_combo.currentText(),
                "download_threads": self.download_threads_spinbox.value(),
                "clear_low_speed": self.clear_low_speed_spinbox.value(),
                "latency_result_limit": self.latency_result_limit_edit.text(),
                "test_latency": self.test_latency_checkbox.isChecked(),
                "test_download": self.test_download_checkbox.isChecked(),
                "test_url": self.test_url_combo.currentText()
            }
            
            # 更新配置
            config["settings"] = settings
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            self.update_feedback_text("配置已保存到 config.json")
        except Exception as e:
            self.update_feedback_text(f"保存配置失败: {str(e)}")
            
    def update_feedback_text(self, text):
        if self.feedback_text:
            self.feedback_text.append(text)
            # 滚动到底部
            scrollbar = self.feedback_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
        
    def update_table_data(self, nodes):
        # 优化表格更新，先禁用排序以提高性能
        self.table_widget.setSortingEnabled(False)
        # 禁用屏幕更新以减少闪烁
        self.table_widget.setUpdatesEnabled(False)
        
        # 设置行数
        self.table_widget.setRowCount(len(nodes))
        
        # 准备所有单元格项，一次性批量创建
        items = []
        for i, node in enumerate(nodes):
            # 更新节点索引
            node["index"] = i + 1
            row_items = [
                QTableWidgetItem(str(i+1)),
                QTableWidgetItem(node.get("type", "")),
                QTableWidgetItem(node.get("name", "")),
                QTableWidgetItem(node.get("server", "")),
                QTableWidgetItem(str(node.get("port", ""))),
                QTableWidgetItem(node.get("cipher", "")),
                QTableWidgetItem(node.get("protocol", "")),
                QTableWidgetItem(node.get("subscription", "")),
                QTableWidgetItem(node.get("latency", "")),
                QTableWidgetItem(node.get("speed", ""))
            ]
            items.append(row_items)
        
        # 一次性设置所有单元格
        for i, row_items in enumerate(items):
            for j, item in enumerate(row_items):
                self.table_widget.setItem(i, j, item)
        
        # 重新启用更新和排序
        self.table_widget.setUpdatesEnabled(True)
        self.table_widget.setSortingEnabled(True)
        
        # 强制刷新表格显示
        self.table_widget.viewport().update()
    
    def on_test_complete(self):
        self.testing = False
        self.auto_test_button.setEnabled(True)
        self.stop_manual_button.setEnabled(False)
        # 测试完成后对节点按延迟进行排序
        self.sort_nodes_by_latency()
        self.update_feedback_text("测试完成，已按延迟排序")
        
        # 停止mihomo内核
        self.stop_mihomo()
        
    def stop_mihomo(self):
        """停止mihomo内核程序"""
        if self.mihomo_process is not None:
            try:
                self.mihomo_process.terminate()
                self.update_feedback_text("Clash内核已停止")
                self.mihomo_process = None
            except Exception as e:
                self.update_feedback_text(f"停止Clash内核失败: {str(e)}")
        else:
            self.update_feedback_text("Clash内核未运行")
    
    def sort_nodes_by_latency(self):
        """对节点按延迟进行排序"""
        try:
            # 创建一个辅助函数来提取延迟值
            def get_latency_value(node):
                latency_str = node.get("latency", "999999 ms")
                if latency_str == "超时" or latency_str == "错误" or not latency_str:
                    return 999999  # 将超时或错误的节点排在最后
                try:
                    return int(latency_str.split()[0])  # 提取数字部分
                except:
                    return 999999  # 解析失败的情况
            
            # 对节点列表进行排序
            self.nodes.sort(key=get_latency_value)
            # 更新表格显示
            self.update_table_data(self.nodes)
        except Exception as e:
            self.update_feedback_text(f"节点排序时发生错误: {str(e)}")
        
    def show_disabled_feature_message(self, feature_name=""):
        """显示功能已禁用的消息"""
        message = f"{feature_name}功能已禁用" if feature_name else "此功能已禁用"
        self.update_feedback_text(message)
        
    def manage_subscription(self):
        # 创建菜单
        menu = QMenu(self)
        subscription_list_action = menu.addAction("订阅列表")
        import_to_nodes_action = menu.addAction("导入到节点列表")
        
        # 显示菜单
        pos = self.subscription_button.mapToGlobal(self.subscription_button.rect().bottomLeft())
        action = menu.exec_(pos)
        
        # 处理菜单选择
        if action == subscription_list_action:
            # 打开订阅列表对话框
            subscriptions = self.load_subscriptions_from_config()  # 从配置文件加载订阅
            list_dialog = SubscriptionListDialog(self, subscriptions)
            if list_dialog.exec_():
                # 保存订阅列表到配置文件
                self.save_subscriptions_to_config(list_dialog.get_subscriptions())
                self.update_feedback_text("订阅列表已保存")
        elif action == import_to_nodes_action:
            self.show_import_subscription_dialog()

    def load_subscriptions_from_config(self):
        """从配置文件加载订阅列表"""
        try:
            if not os.path.exists(self.config_path):
                return []
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            return config.get("subscriptions", [])
        except Exception as e:
            self.update_feedback_text(f"加载订阅列表失败: {str(e)}")
            return []
            
    def save_subscriptions_to_config(self, subscriptions):
        """保存订阅列表到配置文件"""
        try:
            # 获取当前配置
            config = {}
            
            # 如果配置文件已存在，先读取现有配置以保留其他设置
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            # 更新订阅列表
            config["subscriptions"] = subscriptions
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.update_feedback_text(f"保存订阅列表失败: {str(e)}")
            
    def show_import_subscription_dialog(self):
        """显示导入订阅对话框"""
        # 从配置文件加载订阅列表
        subscriptions = self.load_subscriptions_from_config()
        
        if not subscriptions:
            self.update_feedback_text("没有可用的订阅，请先在订阅列表中添加订阅")
            return
            
        # 创建订阅选择对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("选择要导入的订阅")
        dialog.setMinimumWidth(400)
        
        layout = QVBoxLayout(dialog)
        
        # 创建订阅列表
        subscription_list = QListWidget()
        for sub in subscriptions:
            if sub.get("enabled", True):
                item = QListWidgetItem(f"{sub.get('name', '未命名')} - {sub.get('url', '')}")
                item.setData(Qt.UserRole, sub)
                subscription_list.addItem(item)
        
        layout.addWidget(subscription_list)
        
        # 创建按钮
        button_layout = QHBoxLayout()
        import_button = QPushButton("导入")
        cancel_button = QPushButton("取消")
        
        button_layout.addStretch()
        button_layout.addWidget(import_button)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        import_button.clicked.connect(dialog.accept)
        cancel_button.clicked.connect(dialog.reject)
        
        # 显示对话框
        if dialog.exec_() == QDialog.Accepted and subscription_list.currentItem():
            selected_sub = subscription_list.currentItem().data(Qt.UserRole)
            self.load_subscription(selected_sub.get("url", ""))

    def load_subscription(self, url):
        """加载订阅内容并解析节点"""
        if not url:
            self.update_feedback_text("订阅URL为空")
            return
            
        self.update_feedback_text(f"正在加载订阅: {url}")
        
        # 使用线程处理订阅加载，避免界面卡顿
        threading.Thread(
            target=self.load_subscription_thread,
            args=(url,),
            daemon=True
        ).start()
        
    def load_subscription_thread(self, url):
        """在线程中加载订阅内容"""
        try:
            # 获取订阅名称
            subscription_name = self.get_subscription_name_by_url(url)
            
            # 使用requests获取订阅内容
            response = requests.get(url, timeout=15)
            
            # 检查响应状态
            if response.status_code != 200:
                self.signal_manager.update_feedback.emit(f"获取订阅内容失败，状态码: {response.status_code}")
                return
                
            # 保存订阅内容到临时文件
            temp_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_subscription.txt")
            with open(temp_file, 'wb') as f:
                f.write(response.content)
                
            # 解析订阅内容，并指定这是临时文件
            self.parse_node_file_with_api(temp_file, True)
                
        except Exception as e:
            self.signal_manager.update_feedback.emit(f"加载订阅失败: {str(e)}")
            
    def get_subscription_name_by_url(self, url):
        """根据URL获取订阅名称"""
        subscriptions = self.load_subscriptions_from_config()
        for sub in subscriptions:
            if sub.get("url") == url:
                return sub.get("name", "未命名订阅")
        return "未知订阅"

    def start_auto_test(self):
        """启动自动测速过程"""
        if not self.nodes:
            self.update_feedback_text("没有节点可以测试")
            return
            
        if self.testing:
            self.update_feedback_text("测试已在进行中，请等待完成")
            return
            
        # 检查配置文件是否存在
        config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config", "config.yaml")
        if not os.path.exists(config_file):
            self.update_feedback_text("配置文件不存在，请先导入节点生成配置文件")
            return
        
        # 启动mihomo内核（如果尚未启动）
        if self.mihomo_process is None:
            self.start_mihomo()
        
        self.testing = True
        self.auto_test_button.setEnabled(False)
        self.stop_manual_button.setEnabled(True)
        
        # 获取测试设置
        test_latency = self.test_latency_checkbox.isChecked()
        test_download = self.test_download_checkbox.isChecked()
        
        # 创建测试线程
        threading.Thread(
            target=self.run_auto_test,
            args=(test_latency, test_download),
            daemon=True
        ).start()
        
    def run_auto_test(self, test_latency, test_download):
        """在线程中运行自动测速"""
        try:
            # 测试延迟
            if test_latency:
                self.signal_manager.update_feedback.emit("开始测试节点延迟...")
                self.test_latency_for_nodes()
                
            # 测试下载速度
            if test_download:
                self.signal_manager.update_feedback.emit("开始测试节点下载速度...")
                self.test_download_for_nodes()
            
            # 测试完成后对节点进行排序
            self.sort_nodes_by_latency()
            # 完成测试
            self.signal_manager.update_feedback.emit("测试完成，已按延迟排序")
            # 发送测试完成信号，会触发on_test_complete方法自动停止Clash内核
            self.signal_manager.test_complete.emit()
        except Exception as e:
            self.signal_manager.update_feedback.emit(f"测试过程中发生错误: {str(e)}")
            # 发送测试完成信号，会触发on_test_complete方法自动停止Clash内核
            self.signal_manager.test_complete.emit()
            
    def test_latency_for_nodes(self):
        """测试所有节点的延迟"""
        latency_core = self.latency_core_combo.currentText()
        latency_timeout = self.latency_timeout_spinbox.value()
        latency_threads = self.latency_threads_spinbox.value()
        
        # 根据选择的测速内核使用不同的测速方法
        if latency_core == "Clash":
            # 使用Clash API测速
            self.signal_manager.update_feedback.emit("正在使用Clash API测速...")
            clash_port = self.clash_port_edit.text()
            base_url = f"http://127.0.0.1:{clash_port}"
            
            try:
                # 获取Clash中的代理节点列表
                import requests
                proxies_url = f"{base_url}/proxies"
                response = requests.get(proxies_url, timeout=5)
                
                if response.status_code != 200:
                    self.signal_manager.update_feedback.emit(f"获取Clash代理列表失败，状态码: {response.status_code}")
                    return
                
                proxies_data = response.json()
                proxy_nodes = {}
                
                # 获取所有可用代理节点（排除直连、拒绝和选择器等）
                for name, info in proxies_data.get("proxies", {}).items():
                    if info.get("type") != "Direct" and info.get("type") != "Reject" and info.get("type") != "Selector" and info.get("type") != "URLTest":
                        proxy_nodes[name] = info
                
                self.signal_manager.update_feedback.emit(f"在Clash中找到 {len(proxy_nodes)} 个代理节点")
                
                # 创建线程池
                from concurrent.futures import ThreadPoolExecutor
                
                with ThreadPoolExecutor(max_workers=latency_threads) as executor:
                    # 提交所有节点的测试任务
                    futures_dict = {}  # 使用字典保存future和节点索引的对应关系
                    
                    for i, node in enumerate(self.nodes):
                        node_name = node.get("name", "")
                        if node_name in proxy_nodes:
                            future = executor.submit(self.test_node_latency_with_clash_api, node, latency_timeout, clash_port)
                            futures_dict[future] = i  # 保存future和节点索引的对应关系
                        else:
                            # 节点不在Clash中，设置为错误
                            self.nodes[i]["latency"] = "节点不在Clash"
                    
                    # 处理结果
                    for future in futures_dict:
                        try:
                            # 获取延迟测试结果
                            latency = future.result()
                            # 更新节点数据
                            node_index = futures_dict[future]
                            self.nodes[node_index]["latency"] = latency
                            # 更新表格
                            self.signal_manager.update_table.emit(self.nodes)
                        except Exception as e:
                            node_index = futures_dict[future]
                            self.signal_manager.update_feedback.emit(f"测试节点 {self.nodes[node_index].get('name', '未知')} 延迟时发生错误: {str(e)}")
            except Exception as e:
                self.signal_manager.update_feedback.emit(f"使用Clash API测速时发生错误: {str(e)}")
        else:
            # 其他内核测速功能暂未实现
            self.signal_manager.update_feedback.emit(f"暂不支持 {latency_core} 内核的测速功能")

    def test_node_latency_with_clash_api(self, node, timeout, clash_port=None):
        """使用Clash API测试节点延迟"""
        import requests
        import urllib.parse
        import json
        
        # 获取Clash API端口
        if clash_port is None:
            clash_port = self.clash_port_edit.text()
            
        base_url = f"http://127.0.0.1:{clash_port}"
        node_name = node.get("name", "")
        
        try:
            # 获取选定的测试URL
            test_url_key = self.test_url_combo.currentText()
            test_url = TEST_URLS.get(test_url_key, "https://www.google.com/")
            
            # URL编码节点名称
            encoded_name = urllib.parse.quote(node_name)
            delay_url = f"{base_url}/proxies/{encoded_name}/delay?url={test_url}&timeout={timeout * 1000}"
            
            delay_response = requests.get(delay_url, timeout=timeout + 2)
            
            if delay_response.status_code != 200:
                # 尝试解析错误信息
                try:
                    error_data = delay_response.json()
                    error_msg = error_data.get("message", "未知错误")
                    return f"错误: {error_msg}"
                except:
                    return f"错误: HTTP {delay_response.status_code}"
                
            delay_data = delay_response.json()
            
            # 返回测试结果
            delay = delay_data.get("delay", 0)
            if delay > 0:
                # 添加URL标识，表示ChatGPT解锁检测
                url_info = ""
                if "chat.openai.com" in test_url or "api.openai.com" in test_url or "openai.com" in test_url:
                    url_info = " [ChatGPT已解锁]"
                return f"{delay} ms{url_info}"
            else:
                return "超时"
                
        except requests.exceptions.RequestException as e:
            # 连接错误，可能是Clash未启动或端口错误
            if "Connection refused" in str(e):
                return "Clash连接失败"
            elif "timeout" in str(e).lower():
                return "请求超时"
            else:
                return f"连接错误"
        except json.JSONDecodeError:
            return "解析错误"
        except Exception:
            return "未知错误"
    
    def test_download_for_nodes(self):
        """测试所有节点的下载速度"""
        # 获取下载测试设置
        download_core = self.download_core_combo.currentText()
        download_threads = self.download_threads_spinbox.value()
        limit_time = self.limit_time_spinbox.value()
        
        # 创建线程池
        from concurrent.futures import ThreadPoolExecutor
        
        # 根据延迟结果筛选节点
        try:
            latency_limit = int(self.latency_result_limit_edit.text())
            # 过滤掉延迟过高或超时的节点
            valid_nodes = []
            valid_indices = []
            
            for i, node in enumerate(self.nodes):
                latency_str = node.get("latency", "")
                if latency_str and latency_str != "超时" and latency_str != "错误" and "节点不在Clash" not in latency_str:
                    try:
                        latency = int(latency_str.split()[0])  # 提取数字部分
                        if latency <= latency_limit:
                            valid_nodes.append(node)
                            valid_indices.append(i)
                    except:
                        pass
                        
            if not valid_nodes:
                self.signal_manager.update_feedback.emit("没有符合延迟要求的节点可以测试下载速度")
                return
                
            self.signal_manager.update_feedback.emit(f"将测试 {len(valid_nodes)} 个节点的下载速度")
            
            # 根据选择的测速内核使用不同的测速方法
            if download_core == "Clash":
                # 使用Clash API测速
                clash_port = self.clash_port_edit.text()
                
                # 创建线程池，限制并发数
                with ThreadPoolExecutor(max_workers=download_threads) as executor:
                    # 提交所有节点的测试任务
                    futures_dict = {}
                    
                    for i in range(len(valid_nodes)):
                        node = valid_nodes[i]
                        node_index = valid_indices[i]
                        future = executor.submit(
                            self.test_node_download_with_clash_api,
                            node,
                            limit_time,
                            clash_port
                        )
                        futures_dict[future] = node_index
                        
                    # 处理结果
                    for future in futures_dict:
                        try:
                            # 获取下载测试结果
                            speed = future.result()
                            # 更新节点数据
                            node_index = futures_dict[future]
                            self.nodes[node_index]["speed"] = speed
                            # 更新表格
                            self.signal_manager.update_table.emit(self.nodes)
                        except Exception as e:
                            node_index = futures_dict[future]
                            self.signal_manager.update_feedback.emit(f"测试节点 {self.nodes[node_index].get('name', '未知')} 下载速度时发生错误: {str(e)}")
            else:
                # 其他内核测速功能暂未实现
                self.signal_manager.update_feedback.emit(f"暂不支持 {download_core} 内核的测速功能")
                
        except Exception as e:
            self.signal_manager.update_feedback.emit(f"处理延迟结果时发生错误: {str(e)}")

    def run_selected_download_test(self, selected_nodes, selected_indices, core_type):
        """在线程中运行选中节点的下载测试"""
        try:
            self.signal_manager.update_feedback.emit(f"开始测试 {len(selected_nodes)} 个选中节点的下载速度...")
            
            # 获取下载测试设置
            limit_time = self.limit_time_spinbox.value()
            
            # 根据选择的测速内核使用不同的测速方法
            if core_type == "Clash":
                # 使用Clash API测速
                clash_port = self.clash_port_edit.text()
                
                # 创建线程池
                from concurrent.futures import ThreadPoolExecutor
                
                with ThreadPoolExecutor(max_workers=min(5, len(selected_nodes))) as executor:
                    # 提交所有选中节点的测试任务
                    futures_dict = {}
                    
                    for i, node in enumerate(selected_nodes):
                        future = executor.submit(
                            self.test_node_download_with_clash_api,
                            node,
                            limit_time,
                            clash_port
                        )
                        futures_dict[future] = selected_indices[i]
                    
                    # 处理结果
                    for future in futures_dict:
                        try:
                            # 获取下载测试结果
                            speed = future.result()
                            # 更新节点数据
                            node_index = futures_dict[future]
                            self.nodes[node_index]["speed"] = speed
                            # 更新表格
                            self.signal_manager.update_table.emit(self.nodes)
                        except Exception as e:
                            node_index = futures_dict[future]
                            self.signal_manager.update_feedback.emit(f"测试节点 {self.nodes[node_index].get('name', '未知')} 下载速度时发生错误: {str(e)}")
            else:
                # 其他内核测速功能暂未实现
                self.signal_manager.update_feedback.emit(f"暂不支持 {core_type} 内核的测速功能")
            
            # 测试完成后对节点进行排序
            self.sort_nodes_by_latency()
            self.signal_manager.update_feedback.emit("下载测试完成，已按延迟排序")
            # 发送测试完成信号，会触发on_test_complete方法自动停止Clash内核
            self.signal_manager.test_complete.emit()
        except Exception as e:
            self.signal_manager.update_feedback.emit(f"测试过程中发生错误: {str(e)}")
            # 发送测试完成信号，会触发on_test_complete方法自动停止Clash内核
            self.signal_manager.test_complete.emit()

    def test_node_download_with_clash_api(self, node, timeout, clash_port=None):
        """使用Clash API测试节点下载速度"""
        import requests
        import urllib.parse
        import json
        import time
        
        # 获取Clash API端口
        if clash_port is None:
            clash_port = self.clash_port_edit.text()
            
        base_url = f"http://127.0.0.1:{clash_port}"
        node_name = node.get("name", "")
        
        try:
            # 1. 确保节点在Clash中并可用
            proxies_url = f"{base_url}/proxies"
            proxies_response = requests.get(proxies_url, timeout=5)
            
            if proxies_response.status_code != 200:
                return "Clash连接失败"
                
            proxies_data = proxies_response.json()
            if node_name not in proxies_data.get("proxies", {}):
                return "节点不在Clash"
            
            # 2. URL编码节点名称
            encoded_name = urllib.parse.quote(node_name)
            
            # 3. 请求下载测速
            # 获取选定的测试URL
            test_url_key = self.test_url_combo.currentText()
            test_url = TEST_URLS.get(test_url_key, "https://www.google.com/generate_204")
            
            total_delay = 0
            success_count = 0
            test_count = 3
            
            for _ in range(test_count):
                try:
                    delay_url = f"{base_url}/proxies/{encoded_name}/delay?url={test_url}&timeout={timeout * 1000}"
                    delay_response = requests.get(delay_url, timeout=timeout + 2)
                    
                    if delay_response.status_code == 200:
                        delay_data = delay_response.json()
                        delay = delay_data.get("delay", 0)
                        if delay > 0:
                            total_delay += delay
                            success_count += 1
                    
                    # 测试间隔
                    time.sleep(0.5)
                except:
                    pass
            
            # 4. 根据延迟估算下载速度
            if success_count == 0:
                return "测速失败"
                
            avg_delay = total_delay / success_count
            
            # 判断是否为ChatGPT解锁检测
            chatgpt_test = "chat.openai.com" in test_url or "api.openai.com" in test_url or "openai.com" in test_url
            
            # 对于ChatGPT解锁检测，显示解锁状态
            if chatgpt_test:
                if avg_delay < 500:  # 延迟小于500ms认为解锁成功
                    return f"{avg_delay:.0f} ms [ChatGPT已解锁]"
                else:
                    return f"{avg_delay:.0f} ms [访问缓慢]"
            else:
                # 延迟越低，预估速度越高
                if avg_delay < 50:
                    speed_mbps = 10.0  # 优秀连接
                elif avg_delay < 100:
                    speed_mbps = 5.0   # 良好连接
                elif avg_delay < 200:
                    speed_mbps = 2.0   # 一般连接
                else:
                    speed_mbps = 1.0   # 较差连接
                    
                # 5. 返回预估速度
                return f"{speed_mbps:.2f} MB/s (估算)"
                
        except requests.exceptions.RequestException as e:
            # 连接错误，可能是Clash未启动或端口错误
            if "Connection refused" in str(e):
                return "Clash连接失败"
            elif "timeout" in str(e).lower():
                return "请求超时"
            else:
                return f"连接错误"
        except json.JSONDecodeError:
            return "解析错误"
        except Exception:
            return "测速失败"

    def stop_manual_test(self):
        """停止手动测速过程"""
        if self.testing:
            self.testing = False
            self.auto_test_button.setEnabled(True)
            self.stop_manual_button.setEnabled(False)
            self.update_feedback_text("测试已手动停止")
            
            # 停止mihomo内核
            self.stop_mihomo()

    def show_about(self):
        """显示关于信息"""
        about_text = """
        节点自动测速工具(nodesCatch) - V2.0
        
        功能：
        - 支持导入多种格式的节点订阅
        - 支持自动测试节点延迟和下载速度
        - 支持多种节点操作（复制、删除、导出等）
        - 支持自定义测试参数
        
        测速功能已恢复，使用类似v2rayN的测速逻辑
        """
        QMessageBox.about(self, "关于软件", about_text)

    def show_context_menu(self, position):
        # 创建菜单
        menu = QMenu()
        
        # 测试速度相关
        test_latency_action = menu.addAction("测试服务器连接速度(Ctrl+R)")
        test_download_action = menu.addAction("测试服务器下载速度(Ctrl+T)")
        
        # 添加更多菜单选项
        menu.addSeparator()
        paste_action = menu.addAction("从剪贴板导入节点(Ctrl+V)")
        select_all_action = menu.addAction("全选(Ctrl+A)")
        delete_action = menu.addAction("删除选中节点(类型)(Delete)")
        
        # 移除节点相关
        menu.addSeparator()
        remove_duplicates_action = menu.addAction("移除重复节点")
        remove_invalid_action = menu.addAction("移除无效节点")
        remove_low_speed_action = menu.addAction("移除低速节点")
        
        # 导出选项
        menu.addSeparator()
        copy_subscription_action = menu.addAction("导出订阅内容到剪贴板(Ctrl+C)")
        export_base64_action = menu.addAction("导出Base64通用订阅文件")
        export_clash_action = menu.addAction("导出Clash订阅文件")
        
        # 推送到Clash
        menu.addSeparator()
        push_to_clash_action = menu.addAction("选中节点推送到Clash内核")
        
        # 显示菜单
        action = menu.exec_(self.table_widget.mapToGlobal(position))
        if action:
            if action == select_all_action:
                self.table_widget.selectAll()
                self.update_feedback_text("已全选节点")
            elif action == test_latency_action:
                self.test_selected_nodes_latency()
            elif action == test_download_action:
                self.test_selected_nodes_download()
            elif action == paste_action:
                self.import_from_clipboard()
            elif action == delete_action:
                self.delete_selected_nodes()
            elif action == remove_duplicates_action:
                self.remove_duplicate_nodes()
            elif action == remove_invalid_action:
                self.remove_invalid_nodes()
            elif action == remove_low_speed_action:
                self.remove_low_speed_nodes()
            elif action == copy_subscription_action:
                self.export_to_clipboard()
            elif action == export_base64_action:
                self.export_base64_file()
            elif action == export_clash_action:
                self.export_clash_file()
            elif action == push_to_clash_action:
                self.push_to_clash()
            else:
                self.show_disabled_feature_message("菜单")

    def test_selected_nodes_latency(self):
        """测试选中节点的延迟"""
        selected_rows = set(index.row() for index in self.table_widget.selectedIndexes())
        if not selected_rows:
            self.update_feedback_text("请先选择要测试的节点")
            return
            
        if self.testing:
            self.update_feedback_text("测试已在进行中，请等待完成")
            return
            
        # 检查配置文件是否存在
        config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config", "config.yaml")
        if not os.path.exists(config_file):
            self.update_feedback_text("配置文件不存在，请先导入节点生成配置文件")
            return
        
        # 启动mihomo内核（如果尚未启动）
        if self.mihomo_process is None:
            self.start_mihomo()
            
        self.testing = True
        self.auto_test_button.setEnabled(False)
        self.stop_manual_button.setEnabled(True)
        
        # 获取选中的节点
        selected_nodes = [self.nodes[row] for row in selected_rows]
        selected_indices = list(selected_rows)
        
        # 获取测速设置
        latency_core = self.latency_core_combo.currentText()
        
        # 创建测试线程
        threading.Thread(
            target=self.run_selected_latency_test,
            args=(selected_nodes, selected_indices, latency_core),
            daemon=True
        ).start()
        
    def run_selected_latency_test(self, selected_nodes, selected_indices, core_type):
        """在线程中运行选中节点的延迟测试"""
        try:
            self.signal_manager.update_feedback.emit(f"开始测试 {len(selected_nodes)} 个选中节点的延迟...")
            
            latency_timeout = self.latency_timeout_spinbox.value()
            
            # 根据选择的测速内核使用不同的测速方法
            if core_type == "Clash":
                # 使用Clash API测速
                clash_port = self.clash_port_edit.text()
                base_url = f"http://127.0.0.1:{clash_port}"
                
                try:
                    # 获取Clash中的代理节点列表
                    import requests
                    proxies_url = f"{base_url}/proxies"
                    response = requests.get(proxies_url, timeout=5)
                    
                    if response.status_code != 200:
                        self.signal_manager.update_feedback.emit(f"获取Clash代理列表失败，状态码: {response.status_code}")
                        self.signal_manager.test_complete.emit()
                        return
                    
                    proxies_data = response.json()
                    proxy_nodes = {}
                    
                    # 获取所有可用代理节点
                    for name, info in proxies_data.get("proxies", {}).items():
                        if info.get("type") != "Direct" and info.get("type") != "Reject" and info.get("type") != "Selector" and info.get("type") != "URLTest":
                            proxy_nodes[name] = info
                    
                    # 创建线程池
                    from concurrent.futures import ThreadPoolExecutor
                    
                    with ThreadPoolExecutor(max_workers=min(10, len(selected_nodes))) as executor:
                        # 提交所有选中节点的测试任务
                        futures_dict = {}
                        
                        for i, node in enumerate(selected_nodes):
                            node_name = node.get("name", "")
                            if node_name in proxy_nodes:
                                future = executor.submit(self.test_node_latency_with_clash_api, node, latency_timeout, clash_port)
                                futures_dict[future] = selected_indices[i]  # 使用原始索引
                            else:
                                # 节点不在Clash中，设置为错误
                                node_index = selected_indices[i]
                                self.nodes[node_index]["latency"] = "节点不在Clash"
                        
                        # 处理结果
                        for future in futures_dict:
                            try:
                                # 获取延迟测试结果
                                latency = future.result()
                                # 更新节点数据
                                node_index = futures_dict[future]
                                self.nodes[node_index]["latency"] = latency
                                # 更新表格
                                self.signal_manager.update_table.emit(self.nodes)
                            except Exception as e:
                                node_index = futures_dict[future]
                                self.signal_manager.update_feedback.emit(f"测试节点 {self.nodes[node_index].get('name', '未知')} 延迟时发生错误: {str(e)}")
                except Exception as e:
                    self.signal_manager.update_feedback.emit(f"使用Clash API测试选中节点时发生错误: {str(e)}")
            else:
                # 其他内核测速功能暂未实现
                self.signal_manager.update_feedback.emit(f"暂不支持 {core_type} 内核的测速功能")
            
            # 测试完成后对节点进行排序
            self.sort_nodes_by_latency()
            self.signal_manager.update_feedback.emit("延迟测试完成，已按延迟排序")
            # 发送测试完成信号，会触发on_test_complete方法自动停止Clash内核
            self.signal_manager.test_complete.emit()
        except Exception as e:
            self.signal_manager.update_feedback.emit(f"测试过程中发生错误: {str(e)}")
            # 发送测试完成信号，会触发on_test_complete方法自动停止Clash内核
            self.signal_manager.test_complete.emit()
            
    def test_selected_nodes_download(self):
        """测试选中节点的下载速度"""
        selected_rows = set(index.row() for index in self.table_widget.selectedIndexes())
        if not selected_rows:
            self.update_feedback_text("请先选择要测试的节点")
            return
            
        if self.testing:
            self.update_feedback_text("测试已在进行中，请等待完成")
            return
            
        # 检查配置文件是否存在
        config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config", "config.yaml")
        if not os.path.exists(config_file):
            self.update_feedback_text("配置文件不存在，请先导入节点生成配置文件")
            return
        
        # 启动mihomo内核（如果尚未启动）
        if self.mihomo_process is None:
            self.start_mihomo()
            
        self.testing = True
        self.auto_test_button.setEnabled(False)
        self.stop_manual_button.setEnabled(True)
        
        # 获取选中的节点
        selected_nodes = [self.nodes[row] for row in selected_rows]
        selected_indices = list(selected_rows)
        
        # 获取测速设置
        download_core = self.download_core_combo.currentText()
        
        # 创建测试线程
        threading.Thread(
            target=self.run_selected_download_test,
            args=(selected_nodes, selected_indices, download_core),
            daemon=True
        ).start()
        
    def run_selected_download_test(self, selected_nodes, selected_indices, core_type):
        """在线程中运行选中节点的下载测试"""
        try:
            self.signal_manager.update_feedback.emit(f"开始测试 {len(selected_nodes)} 个选中节点的下载速度...")
            
            # 获取下载测试设置
            limit_time = self.limit_time_spinbox.value()
            
            # 根据选择的测速内核使用不同的测速方法
            if core_type == "Clash":
                # 使用Clash API测速
                clash_port = self.clash_port_edit.text()
                
                # 创建线程池
                from concurrent.futures import ThreadPoolExecutor
                
                with ThreadPoolExecutor(max_workers=min(5, len(selected_nodes))) as executor:
                    # 提交所有选中节点的测试任务
                    futures_dict = {}
                    
                    for i, node in enumerate(selected_nodes):
                        future = executor.submit(
                            self.test_node_download_with_clash_api,
                            node,
                            limit_time,
                            clash_port
                        )
                        futures_dict[future] = selected_indices[i]
                    
                    # 处理结果
                    for future in futures_dict:
                        try:
                            # 获取下载测试结果
                            speed = future.result()
                            # 更新节点数据
                            node_index = futures_dict[future]
                            self.nodes[node_index]["speed"] = speed
                            # 更新表格
                            self.signal_manager.update_table.emit(self.nodes)
                        except Exception as e:
                            node_index = futures_dict[future]
                            self.signal_manager.update_feedback.emit(f"测试节点 {self.nodes[node_index].get('name', '未知')} 下载速度时发生错误: {str(e)}")
            else:
                # 其他内核测速功能暂未实现
                self.signal_manager.update_feedback.emit(f"暂不支持 {core_type} 内核的测速功能")
            
            # 测试完成后对节点进行排序
            self.sort_nodes_by_latency()
            self.signal_manager.update_feedback.emit("下载测试完成，已按延迟排序")
            # 发送测试完成信号，会触发on_test_complete方法自动停止Clash内核
            self.signal_manager.test_complete.emit()
        except Exception as e:
            self.signal_manager.update_feedback.emit(f"测试过程中发生错误: {str(e)}")
            # 发送测试完成信号，会触发on_test_complete方法自动停止Clash内核
            self.signal_manager.test_complete.emit()

    def dragEnterEvent(self, event):
        """处理拖动进入事件，接受文件拖放"""
        if event.mimeData().hasUrls():
            # 获取拖放的文件列表
            urls = event.mimeData().urls()
            for url in urls:
                file_path = url.toLocalFile()
                # 检查是否是文件且扩展名是否支持
                if os.path.isfile(file_path):
                    # 检查文件大小，过大的文件可能不是节点配置文件
                    try:
                        file_size = os.path.getsize(file_path)
                        max_size = 10 * 1024 * 1024  # 10MB
                        
                        if file_size > max_size:
                            continue
                    except:
                        pass
                    
                    # 所有文件都接受，具体处理逻辑在dropEvent和parse_node_file_with_api中实现
                    event.acceptProposedAction()
                    return
            # 如果没有找到支持的文件类型，拒绝拖放
            event.ignore()
        else:
            event.ignore()
        
    def dropEvent(self, event):
        """处理文件拖放事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            for url in urls:
                file_path = url.toLocalFile()
                if os.path.isfile(file_path):
                    file_ext = os.path.splitext(file_path)[1].lower()
                    # 支持的文件类型：文本文件、YAML、JSON等常见节点配置文件，以及二进制文件
                    supported_exts = [
                        '.txt', '.yaml', '.yml', '.json', '.conf', '.config', '.ini', '.sub',
                        '.dat', '.bin', '.db', '.data', '.clash', '.v2ray', '.xray', '.ss'
                    ]
                    
                    # 检查文件大小，过大的文件可能不是节点配置文件
                    file_size = os.path.getsize(file_path)
                    max_size = 10 * 1024 * 1024  # 10MB
                    
                    if file_size > max_size:
                        self.update_feedback_text(f"文件过大 ({file_size/1024/1024:.2f}MB)，可能不是节点配置文件")
                        continue
                    
                    if file_ext in supported_exts or not file_ext:
                        self.update_feedback_text(f"正在处理文件: {os.path.basename(file_path)} ({file_ext if file_ext else '无扩展名'})")
                        # 使用线程处理文件解析，避免界面卡顿
                        threading.Thread(
                            target=self.parse_node_file_with_api,
                            args=(file_path, False),  # 明确指定不是临时文件
                            daemon=True
                        ).start()
                    else:
                        # 对于不在支持列表中的扩展名，尝试作为二进制文件处理
                        self.update_feedback_text(f"未知文件类型: {file_ext}，尝试作为二进制文件处理")
                        threading.Thread(
                            target=self.parse_node_file_with_api,
                            args=(file_path, False),  # 明确指定不是临时文件
                            daemon=True
                        ).start()
            event.acceptProposedAction()
        else:
            event.ignore()
            
    def parse_node_file_with_api(self, file_path, is_temp_file=False):
        """使用subconverter API解析节点文件（在线程中运行）"""
        try:
            # 调用API解析节点
            self.update_feedback_text("正在通过API解析节点...")
            
            # 记录开始时间
            import time
            start_time = time.time()
            
            # 使用subconverter API解析节点
            api_url = "http://localhost:25500/sub"
            
            # 构建请求参数
            params = {
                "target": "clash",  # 输出格式为clash
                "url": os.path.abspath(file_path)
            }

            # 发送请求
            try:
                # 增加超时时间，减少超时错误
                response = requests.get(api_url, params=params, timeout=3)
                
                # 检查响应状态
                if response.status_code == 200:
                    # 保存API返回的原始内容，稍后使用
                    clash_text = response.text
                    self.update_feedback_text(f"API响应成功，解析中...")
                    
                    # 使用更快的yaml解析方法
                    clash_config = yaml.safe_load(clash_text)
                    
                    # 计算API请求耗时
                    api_time = time.time() - start_time
                    self.update_feedback_text(f"API请求耗时: {api_time:.2f} 秒")
                else:
                    self.update_feedback_text(f"API请求失败，状态码: {response.status_code}")
                    return
                    
                # 解析代理列表
                nodes = []
                parse_start = time.time()
                
                if 'proxies' in clash_config and isinstance(clash_config['proxies'], list):
                    # 预分配列表大小以提高性能
                    proxies = clash_config['proxies']
                    nodes = []
                    nodes_append = nodes.append  # 局部变量缓存方法引用，提高循环性能
                    
                    for proxy in proxies:
                        node = self.parse_clash_proxy(proxy)
                        if node:
                            nodes_append(node)
                    
                    parse_time = time.time() - parse_start
                    self.update_feedback_text(f"解析 {len(nodes)} 个节点，耗时: {parse_time:.2f} 秒")
                else:
                    self.update_feedback_text("未找到有效的节点列表")
                
                # 如果是临时文件，在解析完成后删除
                if is_temp_file and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        self.update_feedback_text("已删除临时文件")
                    except Exception as e:
                        self.update_feedback_text(f"删除临时文件失败: {str(e)}")
                
                # 将原始内容和解析后的节点一起传递给信号
                self.signal_manager.parsing_complete.emit((nodes, clash_text))
                
            except requests.exceptions.RequestException as e:
                self.update_feedback_text(f"API请求异常: {str(e)}")
                
        except Exception as e:
            self.update_feedback_text(f"解析文件失败: {str(e)}")
            import traceback
            self.update_feedback_text(f"详细错误: {traceback.format_exc()}")
            
            # 如果发生异常且是临时文件，尝试删除
            if is_temp_file and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass
    
    def parse_clash_proxy(self, proxy):
        """解析Clash格式的代理配置"""
        if not isinstance(proxy, dict):
            return None
            
        # 使用deepcopy复制所有配置
        node = copy.deepcopy(proxy)
        
        # 确保基本字段存在
        if 'type' not in node:
            node['type'] = '未知'
        if 'name' not in node:
            node['name'] = '未命名节点'
        if 'server' not in node:
            node['server'] = ''
        if 'port' in node:
            node['port'] = str(node['port'])
        else:
            node['port'] = ''
        
        return node

    def load_config(self):
        """从配置文件中加载配置"""
        try:
            if not os.path.exists(self.config_path):
                self.update_feedback_text("配置文件不存在，将使用默认配置")
                return
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # 检查配置文件中是否包含settings部分
            if "settings" not in config:
                self.update_feedback_text("配置文件中没有设置信息，将使用默认配置")
                return
                
            settings = config["settings"]
            
            # 使用映射表简化配置加载
            checkbox_map = {
                "fast_mode": self.fast_mode_checkbox,
                "test_latency": self.test_latency_checkbox,
                "test_download": self.test_download_checkbox
            }
            
            spinbox_map = {
                "limit_time": self.limit_time_spinbox,
                "peak_speed": self.peak_speed_spinbox,
                "download_progress": self.download_progress_spinbox,
                "latency_threads": self.latency_threads_spinbox,
                "latency_timeout": self.latency_timeout_spinbox,
                "download_threads": self.download_threads_spinbox
            }
            
            # 应用复选框设置
            for key, checkbox in checkbox_map.items():
                checkbox.setChecked(settings.get(key, checkbox.isChecked()))
            
            # 应用数字输入框设置
            for key, spinbox in spinbox_map.items():
                spinbox.setValue(settings.get(key, spinbox.value()))
            
            # 应用浮点数输入框设置
            self.clear_low_speed_spinbox.setValue(settings.get("clear_low_speed", self.clear_low_speed_spinbox.value()))
            
            # 应用文本输入框设置
            self.clash_port_edit.setText(settings.get("clash_port", self.clash_port_edit.text()))
            self.latency_result_limit_edit.setText(str(settings.get("latency_result_limit", self.latency_result_limit_edit.text())))
            
            # 设置下拉框选项时进行检查，防止无效值
            combo_map = {
                "latency_core": self.latency_core_combo,
                "download_core": self.download_core_combo,
                "test_url": self.test_url_combo
            }
            
            for key, combo in combo_map.items():
                value = settings.get(key, combo.currentText())
                if combo.findText(value) >= 0:
                    combo.setCurrentText(value)
            
            self.update_feedback_text("配置已加载")
        except Exception as e:
            self.update_feedback_text(f"加载配置失败: {str(e)}，将使用默认配置")

    def keyPressEvent(self, event):
        """处理键盘事件，实现快捷键功能"""
        # Ctrl+S: 保存配置
        if event.key() == Qt.Key_S and event.modifiers() == Qt.ControlModifier:
            self.save_config()
            
        # Ctrl+A: 全选
        elif event.key() == Qt.Key_A and event.modifiers() == Qt.ControlModifier:
            self.table_widget.selectAll()
            self.update_feedback_text("已全选节点")
            
        # Ctrl+R: 测试延迟
        elif event.key() == Qt.Key_R and event.modifiers() == Qt.ControlModifier:
            self.test_selected_nodes_latency()
            
        # Ctrl+T: 测试下载速度
        elif event.key() == Qt.Key_T and event.modifiers() == Qt.ControlModifier:
            self.test_selected_nodes_download()
            
        # Ctrl+V: 从剪贴板导入节点
        elif event.key() == Qt.Key_V and event.modifiers() == Qt.ControlModifier:
            self.import_from_clipboard()
            
        # Ctrl+C: 导出节点到剪贴板
        elif event.key() == Qt.Key_C and event.modifiers() == Qt.ControlModifier:
            self.export_to_clipboard()
            
        # Delete: 删除选中节点
        elif event.key() == Qt.Key_Delete:
            if self.table_widget.selectedItems():
                self.delete_selected_nodes()
        else:
            # 其他键盘事件传递给父类处理
            super().keyPressEvent(event)

    def start_subconverter(self):
        """启动subconverter程序"""
        try:
            # 获取当前程序目录下的subconverter路径
            subconverter_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "subconverter", "subconverter.exe")
            
            # 检查文件是否存在
            if not os.path.exists(subconverter_path):
                self.update_feedback_text("未找到subconverter程序，请确保subconverter.exe位于程序目录的subconverter文件夹中")
                return
                
            # 启动subconverter进程
            self.subconverter_process = subprocess.Popen(
                subconverter_path,
                cwd=os.path.dirname(subconverter_path),  # 设置工作目录为subconverter所在目录
                creationflags=subprocess.CREATE_NO_WINDOW  # 在Windows上不显示控制台窗口
            )
            
            self.update_feedback_text("subconverter程序已启动")
        except Exception as e:
            self.update_feedback_text(f"启动subconverter失败: {str(e)}")

    def start_mihomo(self):
        """启动mihomo内核程序"""
        try:
            # 检查配置文件是否存在
            config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config", "config.yaml")
            if not os.path.exists(config_file):
                self.update_feedback_text("配置文件不存在，请先导入节点生成配置文件")
                return
                
            # 如果mihomo进程已存在，先终止它
            if self.mihomo_process is not None:
                try:
                    self.mihomo_process.terminate()
                    self.update_feedback_text("已终止旧的mihomo进程")
                    self.mihomo_process = None
                except Exception as e:
                    self.update_feedback_text(f"终止旧的mihomo进程失败: {str(e)}")
            
            # 获取当前程序目录下的verge-mihomo路径
            mihomo_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "verge-mihomo.exe")
            
            # 检查文件是否存在
            if not os.path.exists(mihomo_path):
                self.update_feedback_text("未找到verge-mihomo.exe，请确保该文件位于程序根目录中")
                return
                
            # 启动mihomo进程，并传递-d config参数
            self.mihomo_process = subprocess.Popen(
                [mihomo_path, "-d", "config"],
                cwd=os.path.dirname(mihomo_path),  # 设置工作目录为程序根目录
                creationflags=subprocess.CREATE_NO_WINDOW  # 在Windows上不显示控制台窗口
            )
            
            self.update_feedback_text("verge-mihomo内核已启动，配置目录设置为config文件夹")
        except Exception as e:
            self.update_feedback_text(f"启动verge-mihomo失败: {str(e)}")

    def closeEvent(self, event):
        """关闭窗口时终止所有进程"""
        # 终止subconverter进程
        if self.subconverter_process:
            try:
                self.subconverter_process.terminate()
                self.update_feedback_text("subconverter程序已终止")
            except Exception as e:
                self.update_feedback_text(f"终止subconverter失败: {str(e)}")
                
        # 终止mihomo进程
        if self.mihomo_process:
            try:
                self.mihomo_process.terminate()
                self.update_feedback_text("mihomo内核已终止")
            except Exception as e:
                self.update_feedback_text(f"终止mihomo内核失败: {str(e)}")
                
        event.accept()

    def on_parsing_complete(self, data):
        """处理节点解析完成事件"""
        if isinstance(data, tuple) and len(data) == 2:
            nodes, clash_text = data
            
            if nodes:
                # 记录开始时间
                import time
                start_time = time.time()
                
                # 添加到现有节点列表
                self.nodes.extend(nodes)
                
                # 更新表格（这是最耗时的操作）
                self.update_feedback_text(f"正在更新表格，请稍候...")
                self.update_table_data(self.nodes)
                
                # 在表格更新后生成config.yaml，包含所有节点
                try:
                    # 创建新的配置，只包含所有节点和基本设置
                    full_clash_config = {
                        "mixed-port": 7890,
                        "external-controller": "127.0.0.1:9090",
                        "proxies": self.nodes  # 使用所有节点，而不仅仅是新导入的节点
                    }
                    
                    # 创建config.yaml，包含所有节点
                    config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config", "config.yaml")
                    with open(config_file, 'w', encoding='utf-8') as f:
                        yaml.dump(full_clash_config, f, allow_unicode=True, sort_keys=False)
                    
                    # 计算耗时
                    elapsed_time = time.time() - start_time
                    self.update_feedback_text(f"成功导入 {len(nodes)} 个节点，耗时 {elapsed_time:.2f} 秒")
                    self.update_feedback_text(f"已生成配置文件: {config_file}")
                except Exception as e:
                    self.update_feedback_text(f"生成配置文件时出错: {str(e)}")
                    import traceback
                    self.update_feedback_text(f"详细错误: {traceback.format_exc()}")
            else:
                self.update_feedback_text("未在文件中找到有效节点")
        else:
            self.update_feedback_text("解析数据格式错误")

    def on_header_clicked(self, column):
        """处理表头点击事件，实现点击列名排序功能"""
        # 如果点击的是之前已排序的列，则切换排序顺序
        if self.sort_column == column:
            self.sort_order = Qt.AscendingOrder if self.sort_order == Qt.DescendingOrder else Qt.DescendingOrder
        else:
            # 否则设置为升序
            self.sort_column = column
            self.sort_order = Qt.AscendingOrder
            
        # 根据不同的列类型进行排序
        self.sort_nodes_by_column(column, self.sort_order)
        
    def sort_nodes_by_column(self, column, order):
        """根据指定列和顺序对节点进行排序"""
        if not self.nodes:
            return
            
        # 获取列标题
        header_labels = [self.table_widget.horizontalHeaderItem(i).text() for i in range(self.table_widget.columnCount())]
        column_name = header_labels[column]
        
        # 根据不同列定义排序键函数
        def get_sort_key(node):
            if column == 0:  # No列，使用索引排序
                return int(node.get("index", 0))
            elif column == 1:  # 类型
                return node.get("type", "")
            elif column == 2:  # 别名
                return node.get("name", "")
            elif column == 3:  # 服务器地址
                return node.get("server", "")
            elif column == 4:  # 端口
                try:
                    return int(node.get("port", 0))
                except:
                    return 0
            elif column == 5:  # 加密方式
                return node.get("cipher", "")
            elif column == 6:  # 传输协议
                return node.get("protocol", "")
            elif column == 7:  # 订阅
                return node.get("subscription", "")
            elif column == 8:  # 测试结果(延迟)
                latency_str = node.get("latency", "999999 ms")
                if latency_str == "超时" or latency_str == "错误" or not latency_str:
                    return 999999
                try:
                    return int(latency_str.split()[0])
                except:
                    return 999999
            elif column == 9:  # 峰值速度
                speed_str = node.get("speed", "")
                if not speed_str:
                    return 0
                try:
                    if "MB/s" in speed_str:
                        return float(speed_str.split()[0]) * 1024  # 转换为KB/s便于比较
                    elif "KB/s" in speed_str:
                        return float(speed_str.split()[0])
                    else:
                        return 0
                except:
                    return 0
            else:
                return 0
                
        # 对节点列表进行排序
        reverse = (order == Qt.DescendingOrder)
        self.nodes.sort(key=get_sort_key, reverse=reverse)
        
        # 更新节点索引
        for i, node in enumerate(self.nodes):
            node["index"] = i + 1
            
        # 更新表格显示
        self.update_table_data(self.nodes)
        self.update_feedback_text(f"已按{column_name}{'降序' if reverse else '升序'}排序")

    def delete_selected_nodes(self):
        """删除选中的节点"""
        # 获取选中的行
        selected_rows = set(index.row() for index in self.table_widget.selectedIndexes())
        if not selected_rows:
            self.update_feedback_text("没有选中的节点")
            return
            
        # 确认删除
        reply = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除选中的 {len(selected_rows)} 个节点吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 从高到低排序，以便从后向前删除（避免索引变化）
            selected_rows = sorted(selected_rows, reverse=True)
            
            # 删除节点
            for row in selected_rows:
                if 0 <= row < len(self.nodes):
                    del self.nodes[row]
                    
            # 更新表格
            self.update_table_data(self.nodes)
            self.update_feedback_text(f"已删除 {len(selected_rows)} 个节点")
            
    def remove_duplicate_nodes(self):
        """移除重复节点"""
        if not self.nodes:
            self.update_feedback_text("没有节点可以处理")
            return
            
        # 使用集合来检测重复
        unique_nodes = []
        unique_keys = set()
        
        # 遍历所有节点
        for node in self.nodes:
            # 创建唯一键（使用服务器地址和端口）
            key = f"{node.get('server', '')}:{node.get('port', '')}"
            
            # 如果键不在集合中，则添加到结果列表
            if key not in unique_keys:
                unique_keys.add(key)
                unique_nodes.append(node)
                
        # 计算删除的节点数量
        removed_count = len(self.nodes) - len(unique_nodes)
        
        # 更新节点列表
        self.nodes = unique_nodes
        
        # 更新表格
        self.update_table_data(self.nodes)
        self.update_feedback_text(f"已移除 {removed_count} 个重复节点")
        
    def remove_invalid_nodes(self):
        """移除无效节点"""
        if not self.nodes:
            self.update_feedback_text("没有节点可以处理")
            return
            
        # 过滤有效节点
        valid_nodes = []
        
        # 遍历所有节点
        for node in self.nodes:
            # 检查节点是否有服务器地址和端口
            if node.get('server') and node.get('port'):
                valid_nodes.append(node)
                
        # 计算删除的节点数量
        removed_count = len(self.nodes) - len(valid_nodes)
        
        # 更新节点列表
        self.nodes = valid_nodes
        
        # 更新表格
        self.update_table_data(self.nodes)
        self.update_feedback_text(f"已移除 {removed_count} 个无效节点")
        
    def remove_low_speed_nodes(self):
        """移除低速节点"""
        if not self.nodes:
            self.update_feedback_text("没有节点可以处理")
            return
            
        # 获取速度阈值（MB/s）
        threshold = self.clear_low_speed_spinbox.value()
        
        # 过滤高速节点
        high_speed_nodes = []
        
        # 遍历所有节点
        for node in self.nodes:
            speed_str = node.get("speed", "")
            
            # 如果没有测速结果，保留节点
            if not speed_str:
                high_speed_nodes.append(node)
                continue
                
            try:
                # 解析速度值
                if "MB/s" in speed_str:
                    speed = float(speed_str.split()[0])
                    if speed >= threshold:
                        high_speed_nodes.append(node)
                elif "KB/s" in speed_str:
                    speed = float(speed_str.split()[0]) / 1024  # 转换为MB/s
                    if speed >= threshold:
                        high_speed_nodes.append(node)
                else:
                    # 无法解析的格式，保留节点
                    high_speed_nodes.append(node)
            except:
                # 解析失败，保留节点
                high_speed_nodes.append(node)
                
        # 计算删除的节点数量
        removed_count = len(self.nodes) - len(high_speed_nodes)
        
        # 更新节点列表
        self.nodes = high_speed_nodes
        
        # 更新表格
        self.update_table_data(self.nodes)
        self.update_feedback_text(f"已移除 {removed_count} 个低速节点（低于 {threshold} MB/s）")

    def import_from_clipboard(self):
        """从剪贴板导入节点"""
        # 获取剪贴板内容
        clipboard = QApplication.clipboard()
        clipboard_text = clipboard.text().strip()
        
        if not clipboard_text:
            self.update_feedback_text("剪贴板内容为空")
            return
            
        # 保存到临时文件
        temp_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_clipboard.txt")
        try:
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(clipboard_text)
                
            # 解析节点
            self.update_feedback_text("正在解析剪贴板内容...")
            
            # 使用线程处理解析，避免界面卡顿
            threading.Thread(
                target=self.parse_node_file_with_api,
                args=(temp_file, True),  # 传递is_temp_file=True参数
                daemon=True
            ).start()
            
        except Exception as e:
            self.update_feedback_text(f"处理剪贴板内容失败: {str(e)}")
            # 尝试删除临时文件
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except:
                pass

    def export_to_clipboard(self):
        """导出节点到剪贴板"""
        # 获取选中的节点
        selected_rows = set(index.row() for index in self.table_widget.selectedIndexes())
        
        # 收集选中节点的信息
        selected_nodes = [self.nodes[row] for row in sorted(selected_rows) if 0 <= row < len(self.nodes)]
        
        if not selected_nodes:
            self.update_feedback_text("没有有效的选中节点")
            return
                
        # 创建临时文件路径
        temp_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_nodes.yaml")
        
        try:
            # 将节点数据转换为Clash格式并写入临时文件
            clash_config = {
                "proxies": selected_nodes
            }
            
            with open(temp_file, 'w', encoding='utf-8') as f:
                yaml.dump(clash_config, f, allow_unicode=True, sort_keys=False)
            
            # 通过request访问api获取节点信息
            api_url = "http://localhost:25500/sub"
            params = {
                "target": "mixed",
                "emoji": "false",
                "url": os.path.abspath(temp_file)
            }

            # 发送请求获取转换后的节点内容
            response = requests.get(api_url, params=params, timeout=3)
            
            if response.status_code == 200:
                # 将转换后的内容复制到剪贴板
                clipboard = QApplication.clipboard()
                clipboard.setText(response.text)
                
                self.update_feedback_text(f"已将 {len(selected_nodes)} 个节点配置复制到剪贴板")
            else:
                self.update_feedback_text(f"API请求失败，状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            self.update_feedback_text(f"API请求异常: {str(e)}")
        except Exception as e:
            self.update_feedback_text(f"导出节点到剪贴板失败: {str(e)}")
        finally:
            # 删除临时文件
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception as e:
                self.update_feedback_text(f"删除临时文件失败: {str(e)}")

    def export_base64_file(self):
        """导出Base64格式的订阅文件"""
        # 获取选中的节点
        selected_rows = set(index.row() for index in self.table_widget.selectedIndexes())
        
        # 收集选中节点的信息
        selected_nodes = [self.nodes[row] for row in sorted(selected_rows) if 0 <= row < len(self.nodes)]
                
        # 创建临时文件路径
        temp_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_nodes.yaml")
        
        try:
            # 将节点数据转换为Clash格式并写入临时文件
            clash_config = {
                "proxies": selected_nodes
            }
            
            with open(temp_file, 'w', encoding='utf-8') as f:
                yaml.dump(clash_config, f, allow_unicode=True, sort_keys=False)
            
            # 询问保存文件位置
            save_path, _ = QFileDialog.getSaveFileName(
                self, 
                "保存Base64订阅文件", 
                os.path.join(os.path.expanduser("~"), "base64.txt"),
                "Text Files (*.txt);;All Files (*)"
            )
            
            if not save_path:
                self.update_feedback_text("已取消保存")
                return
                
            # 通过request访问api获取节点信息
            api_url = "http://localhost:25500/sub"
            params = {
                "target": "mixed",  # 使用base64格式
                "emoji": "false",
                "url": os.path.abspath(temp_file)
            }

            # 发送请求获取转换后的节点内容
            response = requests.get(api_url, params=params, timeout=3)
            
            if response.status_code == 200:
                # 将内容保存到文件
                with open(save_path, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                self.update_feedback_text(f"已将 {len(selected_nodes)} 个节点的Base64订阅文件保存到: {save_path}")
            else:
                self.update_feedback_text(f"API请求失败，状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            self.update_feedback_text(f"API请求异常: {str(e)}")
        except Exception as e:
            self.update_feedback_text(f"导出Base64订阅文件失败: {str(e)}")
        finally:
            # 删除临时文件
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception as e:
                self.update_feedback_text(f"删除临时文件失败: {str(e)}")

    def export_clash_file(self):
        """导出Clash格式的配置文件"""
        # 获取选中的节点
        selected_rows = set(index.row() for index in self.table_widget.selectedIndexes())
        
        # 收集选中节点的信息
        selected_nodes = [self.nodes[row] for row in sorted(selected_rows) if 0 <= row < len(self.nodes)]
                
        # 创建临时文件路径
        temp_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_nodes.yaml")
        
        try:
            # 将节点数据转换为Clash格式并写入临时文件
            clash_config = {
                "proxies": selected_nodes
            }
            
            with open(temp_file, 'w', encoding='utf-8') as f:
                yaml.dump(clash_config, f, allow_unicode=True, sort_keys=False)
            
            # 询问保存文件位置
            save_path, _ = QFileDialog.getSaveFileName(
                self, 
                "保存Clash配置文件", 
                os.path.join(os.path.expanduser("~"), "clash.yaml"),
                "YAML Files (*.yaml *.yml);;All Files (*)"
            )
            
            if not save_path:
                self.update_feedback_text("已取消保存")
                return
                
            # 通过request访问api获取节点信息
            api_url = "http://localhost:25500/sub"
            params = {
                "target": "clash",  # 使用clash格式
                "emoji": "false",
                "url": os.path.abspath(temp_file)
            }

            # 发送请求获取转换后的节点内容
            response = requests.get(api_url, params=params, timeout=3)
            
            if response.status_code == 200:
                # 将内容保存到文件
                with open(save_path, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                self.update_feedback_text(f"已将 {len(selected_nodes)} 个节点的Clash配置文件保存到: {save_path}")
            else:
                self.update_feedback_text(f"API请求失败，状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            self.update_feedback_text(f"API请求异常: {str(e)}")
        except Exception as e:
            self.update_feedback_text(f"导出Clash配置文件失败: {str(e)}")
        finally:
            # 删除临时文件
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception as e:
                self.update_feedback_text(f"删除临时文件失败: {str(e)}")

    def push_to_clash(self):
        """将选中的节点推送到Clash内核"""
        # 获取选中的节点
        selected_rows = set(index.row() for index in self.table_widget.selectedIndexes())
        
        # 如果没有选中的行，显示全部节点数量
        if not selected_rows:
            nodes_count = len(self.nodes)
            self.update_feedback_text(f"未选中节点，共有 {nodes_count} 个节点")
        else:
            self.update_feedback_text(f"已选中 {len(selected_rows)} 个节点")
            
        # 显示功能已禁用的消息
        self.show_disabled_feature_message("推送到Clash内核")

    def convert_nodes_to_links(self, nodes):
        """使用subconverter API将节点转换为链接列表"""
        self.update_feedback_text("节点转换为链接功能已禁用")
        return []
            
    def convert_nodes_to_clash_config(self, nodes):
        """使用subconverter API将节点转换为Clash配置"""
        self.update_feedback_text("节点转换为Clash配置功能已禁用")
        return None

def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 