[[custom_groups]]
name = "🔰 节点选择"
type = "select"
rule = ["[]♻️ 自动选择", "[]🎯 全球直连", ".*"]

[[custom_groups]]
name = "♻️ 自动选择"
type = "url-test"
rule = [".*"]
url = "https://www.google.com/"
interval = 1

[[custom_groups]]
name = "🎥 NETFLIX"
type = "select"
rule = [
    "[]🔰 节点选择",
    "[]♻️ 自动选择",
    "[]🎯 全球直连",
    ".*"
]

[[custom_groups]]
name = "⛔️ 广告拦截"
type = "select"
rule = ["[]🛑 全球拦截",
"[]🎯 全球直连",
"[]🔰 节点选择"
]

[[custom_groups]]
name = "🚫 运营劫持"
type = "select"
rule = ["[]🛑 全球拦截",
"[]🎯 全球直连",
"[]🔰 节点选择"]

[[custom_groups]]
name = "🌍 国外媒体"
type = "select"
rule = ["[]🔰 节点选择",
"[]♻️ 自动选择",
"[]🎯 全球直连",
".*"]

[[custom_groups]]
name = "🌏 国内媒体"
type = "select"
rule = ["[]🎯 全球直连",
"(HGC|HKBN|PCCW|HKT|深台|彰化|新北|台|hk|港|tw)",
"[]🔰 节点选择"]

[[custom_groups]]
name = "Ⓜ️ 微软服务"
type = "select"
rule = ["[]🎯 全球直连",
"[]🔰 节点选择",
".*"]

[[custom_groups]]
name = "📲 电报信息"
type = "select"
rule = ["[]🔰 节点选择",
"[]🎯 全球直连",
".*"]

[[custom_groups]]
name = "🍎 苹果服务"
type = "select"
rule = ["[]🔰 节点选择",
"[]🎯 全球直连",
"[]♻️ 自动选择",
".*"]

[[custom_groups]]
name = "🎯 全球直连"
type = "select"
rule = ["[]DIRECT"]

[[custom_groups]]
name = "🛑 全球拦截"
type = "select"
rule = ["[]REJECT", "[]DIRECT"]

[[custom_groups]]
name = "🐟 漏网之鱼"
type = "select"
rule = ["[]🔰 节点选择",
"[]🎯 全球直连",
"[]♻️ 自动选择",
".*"]
