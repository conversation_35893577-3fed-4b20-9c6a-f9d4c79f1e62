[custom]
;This is an example external configuration file
;All possible customization settings are shown below

;Options for custom groups
;custom_proxy_group=Proxy`select`.*`[]AUTO`[]DIRECT`.*
;custom_proxy_group=AUTO`url-test`.*`http://www.gstatic.com/generate_204`300
;custom_proxy_group=google`select`.*
;custom_proxy_group=netflix`select`.*
;custom_proxy_group=动画疯`select`(深台|彰化|新北|台)
;custom_proxy_group=fox+`select`(HGC|HKBN|PCCW|HKT|深台|彰化|新北|台|新加坡|sg|hk|tw)
;custom_proxy_group=美区影视`select`(美|美国)
;custom_proxy_group=Global_media`select`.*
;custom_proxy_group=Domestic`select`[]DIRECT`[]Proxy
;custom_proxy_group=Apple`select`[]DIRECT`[]Proxy
;custom_proxy_group=Final`select`[]Proxy`[]DIRECT
;custom_proxy_group=屏蔽广告`select`[]REJECT`[]DIRECT
;custom_proxy_group=UnblockNeteaseMusic`select`云音乐解锁`[]DIRECT
;custom_proxy_group=Telegram`select`新加坡`[]Proxy
custom_proxy_group=!!import:snippets/groups_forcerule.txt

;Options for custom rulesets
enable_rule_generator=false
overwrite_original_rules=false
;ruleset=DIRECT,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/Unbreak.list
;ruleset=⛔️ 广告拦截,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/Advertising.list
;ruleset=🚫 运营劫持,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/Hijacking.list
;ruleset=🌌 YouTube,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/Media/YouTube.list
;ruleset=🎥 NETFLIX,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/Media/Netflix.list
;ruleset=HBO,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/Media/HBO.list
;ruleset=Fox,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/Media/Fox.list
;ruleset=🌍 国外媒体,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/GlobalMedia.list
;ruleset=🌏 港台媒体,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/HKMTMedia.list
;ruleset=📲 电报信息,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/Telegram.list
;ruleset=🔰 节点选择,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/Global.list
;ruleset=🍎 苹果服务,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/Apple.list
;ruleset=DIRECT,https://raw.githubusercontent.com/ConnersHua/Profiles/master/Surge/Ruleset/China.list

;Options for custom base configuration file
clash_rule_base=base/forcerule.yml
;surge_rule_base=base/surge.conf
;surfboard_rule_base=base/surfboard.conf
;mellow_rule_base=base/mellow.conf
;quan_rule_base=base/quan.conf
;quanx_rule_base=base/quanx.conf
;loon_rule_base=base/loon.conf
;sssub_rule_base=base/shadowsocks_base.json
;singbox_rule_base=base/singbox.json

;Options for renaming nodes
;rename=Test-(.*?)-(.*?)-(.*?)\((.*?)\)@\1\4x测试线路_自\2到\3
;rename=\(?((x|X)?(\d+)(\.?\d+)?)((\s?倍率?)|(x|X))\)?@$1x

;Options for adding emojis
;add_emoji=true
;remove_old_emoji=true
;emoji=(流量|时间|应急),🏳️‍🌈
;emoji=阿根廷,🇦🇷

;Options for filtering nodes
;include_remarks=
;exclude_remarks=

;[template]
;;variables in the local scope
;clash.dns.port=5353
