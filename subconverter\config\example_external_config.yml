custom:
  enable_rule_generator: false
  overwrite_original_rules: false

  proxy_groups:
  - {import: snippets/groups_forcerule.txt}

#  rulesets:
#  - {import: snippets/ruleset_remote.txt}

  clash_rule_base: base/forcerule.yml
#  surge_rule_base: base/surge.conf
#  surfboard_rule_base: base/surfboard.conf
#  mellow_rule_base: base/mellow.conf
#  quan_rule_base: base/quan.conf
#  quanx_rule_base: base/quanx.conf
#  loon_rule_base: base/loon.conf
#  sssub_rule_base: base/shadowsocks_base.json
#  singbox_rule_base: base/singbox.json

#  rename_node:
#  - {import: snippet/rename.txt}

#  add_emoji: true
#  remove_old_emoji: true
#  emojis:
#  - {import: snippets/emoji.txt}

#  include_remarks: []
#  exclude_remarks: []
#  template_args:
#  - {key: clash.dns.port, value: 5353}
